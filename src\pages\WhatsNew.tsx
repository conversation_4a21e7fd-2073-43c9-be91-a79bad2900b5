
import React, { useState, useEffect } from 'react';
import { supabase } from "@/integrations/supabase/client";
import { Loader2, Calendar, Download, Info, ArrowUp, ArrowDown, Code, CheckCircle, Star, Tag, Zap, Rocket, Sparkles, Crown, Award, Shield } from "lucide-react";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import { useToast } from "@/components/ui/use-toast";

interface UpdateItem {
  varizon: string;
  name: string | null;
  changelog: string | null;
  release_at: string | null;
  link: string | null;
}

const WhatsNew = () => {
  const { toast: notifyToast } = useToast();
  const [updates, setUpdates] = useState<UpdateItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [latestVersion, setLatestVersion] = useState<string | null>(null);
  const [expanded, setExpanded] = useState<string | null>(null);

  useEffect(() => {
    const fetchUpdates = async () => {
      try {
        const { data, error } = await supabase
          .from('update')
          .select('varizon, name, changelog, release_at, link')
          .order('release_at', { ascending: false });

        if (error) throw error;
        setUpdates(data || []);

        // Set the latest version for comparison
        if (data && data.length > 0) {
          setLatestVersion(data[0].varizon);
          // Automatically expand the latest version
          setExpanded(data[0].varizon);
        }
      } catch (error) {
        console.error('Error fetching updates:', error);
        toast.error('Failed to load update history');
      } finally {
        setLoading(false);
      }
    };

    fetchUpdates();
  }, []);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Unknown date';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  const handleDownload = async (link: string | null, version: string | null) => {
    if (!link) {
      toast.error('Download link not available');
      return;
    }

    try {
      // Call the increment_counter function
      const { data, error } = await supabase.rpc('increment_counter');

      if (error) {
        console.error('Error incrementing download counter:', error);
        toast.error('Failed to process download');
      } else {
        console.log('Download count increased to:', data);

        // Open the download link in a new tab
        window.open(link, '_blank');
        toast.success('Download started');
      }
    } catch (error) {
      console.error('Error during download:', error);
      toast.error('Failed to process download');
    }
  };

  const toggleExpand = (version: string) => {
    if (expanded === version) {
      setExpanded(null);
    } else {
      setExpanded(version);
    }
  };

  // Get icon based on version number with more creative icons
  const getUpdateIcon = (index: number, isLatest: boolean) => {
    if (isLatest) return Crown; // Crown for latest version
    const icons = [Rocket, Zap, Sparkles, Star, Award, Shield, CheckCircle, Code];
    return icons[index % icons.length];
  };

  // Generate advanced gradient color classes for the timeline nodes
  const getNodeGradient = (index: number, isLatest: boolean) => {
    if (isLatest) {
      return 'bg-gradient-to-br from-yellow-400 via-yellow-500 to-orange-500 shadow-[0_0_20px_rgba(251,191,36,0.6)]';
    }
    const gradients = [
      'bg-gradient-to-br from-purple-500 via-purple-600 to-indigo-600 shadow-[0_0_15px_rgba(147,51,234,0.5)]',
      'bg-gradient-to-br from-blue-500 via-blue-600 to-cyan-600 shadow-[0_0_15px_rgba(59,130,246,0.5)]',
      'bg-gradient-to-br from-emerald-500 via-emerald-600 to-teal-600 shadow-[0_0_15px_rgba(16,185,129,0.5)]',
      'bg-gradient-to-br from-pink-500 via-pink-600 to-rose-600 shadow-[0_0_15px_rgba(236,72,153,0.5)]',
      'bg-gradient-to-br from-indigo-500 via-indigo-600 to-purple-600 shadow-[0_0_15px_rgba(99,102,241,0.5)]',
    ];
    return gradients[index % gradients.length];
  };

  // Get connecting line style based on position
  const getConnectorStyle = (index: number) => {
    const styles = [
      'from-purple-400/60 via-purple-500/40 to-purple-600/60',
      'from-blue-400/60 via-blue-500/40 to-cyan-500/60',
      'from-emerald-400/60 via-emerald-500/40 to-teal-500/60',
      'from-pink-400/60 via-pink-500/40 to-rose-500/60',
    ];
    return styles[index % styles.length];
  };

  // Enhanced animation variants for timeline items
  const timelineItemVariants = {
    hidden: {
      opacity: 0,
      x: -100,
      scale: 0.8
    },
    show: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.22, 1, 0.36, 1],
        type: "spring",
        stiffness: 100
      }
    }
  };

  // Floating animation for timeline elements
  const floatingAnimation = {
    y: [0, -10, 0],
    transition: {
      duration: 4,
      repeat: Infinity,
      ease: "easeInOut"
    }
  };

  return (
    <div className="min-h-screen bg-[#111111] text-gray-300 pt-20 pb-16">
      {/* Background Effects */}
      <div className="absolute inset-0 z-0 pointer-events-none" style={{
        background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
      }}></div>

      {/* Enhanced Animated particles */}
      {[...Array(12)].map((_, i) => (
        <motion.div
          key={i}
          className={`absolute rounded-full ${i % 3 === 0 ? 'bg-yellow-400/10' : i % 3 === 1 ? 'bg-purple-400/10' : 'bg-blue-400/10'}`}
          style={{
            width: Math.random() * 40 + 15,
            height: Math.random() * 40 + 15,
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -120, 0],
            x: [0, Math.random() * 60 - 30, 0],
            opacity: [0, 0.4, 0],
            scale: [0.5, 1, 0.5],
            rotate: [0, 360, 0],
          }}
          transition={{
            duration: Math.random() * 20 + 15,
            repeat: Infinity,
            delay: Math.random() * 8,
            ease: "easeInOut"
          }}
        />
      ))}

      {/* Gradient Orbs */}
      {[...Array(4)].map((_, i) => (
        <motion.div
          key={`orb-${i}`}
          className="absolute rounded-full blur-3xl opacity-20"
          style={{
            width: 200 + Math.random() * 100,
            height: 200 + Math.random() * 100,
            top: `${20 + i * 20}%`,
            left: `${10 + i * 20}%`,
            background: i % 2 === 0
              ? 'radial-gradient(circle, rgba(168, 85, 247, 0.4) 0%, transparent 70%)'
              : 'radial-gradient(circle, rgba(251, 191, 36, 0.3) 0%, transparent 70%)'
          }}
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 25 + i * 5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: i * 3
          }}
        />
      ))}

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="mb-6">
            <span className="bg-[#1a1a1a] border border-gray-700 text-purple-400 px-4 py-1 rounded-full text-xs sm:text-sm font-medium">
              Latest Updates
            </span>
          </div>
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-semibold text-white leading-tight mb-4">
            What's New in <span className="text-purple-400">Pegasus Tool</span>
          </h1>
          <p className="text-base sm:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto">
            Track our software updates and new features as we continue to evolve
          </p>
        </motion.div>

        {loading ? (
          <div className="flex justify-center items-center py-24">
            <Loader2 className="h-12 w-12 text-purple-400 animate-spin" />
          </div>
        ) : updates.length > 0 ? (
          <div className="relative max-w-6xl mx-auto">
            {/* Advanced Timeline Background */}
            <div className="absolute left-6 md:left-12 top-0 bottom-0 w-0.5 bg-gradient-to-b from-transparent via-gray-600/30 to-transparent"></div>

            {/* Animated Timeline Core */}
            <motion.div
              className="absolute left-6 md:left-12 top-0 w-0.5 bg-gradient-to-b from-purple-400 via-purple-500 to-purple-600"
              initial={{ height: 0 }}
              animate={{ height: "100%" }}
              transition={{ duration: 2, ease: "easeInOut" }}
            />

            {/* Glowing Timeline Effects */}
            <div className="absolute left-5 md:left-11 top-0 bottom-0 w-2.5 bg-gradient-to-b from-purple-400/20 via-purple-500/10 to-purple-600/20 blur-sm"></div>

            {/* Enhanced Timeline Particles */}
            {[...Array(8)].map((_, i) => (
              <motion.div
                key={`timeline-particle-${i}`}
                className={`absolute w-2 h-2 rounded-full left-6 md:left-12 ${
                  i % 3 === 0 ? 'bg-yellow-400' : i % 3 === 1 ? 'bg-purple-400' : 'bg-blue-400'
                }`}
                style={{ top: `${15 + i * 12}%` }}
                animate={{
                  y: [0, -15, 0],
                  opacity: [0.2, 1, 0.2],
                  scale: [0.5, 1.5, 0.5],
                  boxShadow: [
                    '0 0 0 rgba(168, 85, 247, 0)',
                    '0 0 20px rgba(168, 85, 247, 0.8)',
                    '0 0 0 rgba(168, 85, 247, 0)'
                  ]
                }}
                transition={{
                  duration: 4 + i * 0.7,
                  repeat: Infinity,
                  delay: i * 1.2,
                  ease: "easeInOut"
                }}
              />
            ))}

            {/* Timeline Progress Indicator */}
            <motion.div
              className="absolute left-5 md:left-11 top-0 w-2.5 bg-gradient-to-b from-yellow-400/30 via-purple-400/30 to-blue-400/30 rounded-full"
              initial={{ height: 0 }}
              animate={{ height: "100%" }}
              transition={{ duration: 3, ease: "easeInOut", delay: 0.5 }}
            />

            <motion.div
              className="space-y-16 md:space-y-20"
              initial="hidden"
              animate="show"
              variants={{
                hidden: {},
                show: {
                  transition: {
                    staggerChildren: 0.2
                  }
                }
              }}
            >
              {updates.map((update, index) => {
                const isLatest = update.varizon === latestVersion;
                const isExpanded = expanded === update.varizon;
                const UpdateIcon = getUpdateIcon(index, isLatest);
                const nodeGradient = getNodeGradient(index, isLatest);
                const connectorStyle = getConnectorStyle(index);

                return (
                  <motion.div
                    key={update.varizon}
                    className="relative"
                    variants={timelineItemVariants}
                  >
                    {/* Advanced Timeline Node */}
                    <div className="flex items-start gap-8 md:gap-12">
                      {/* Node Container */}
                      <div className="relative flex-shrink-0">
                        {/* Node Glow Effect */}
                        <motion.div
                          className={`absolute inset-0 w-16 h-16 rounded-full ${nodeGradient.split(' ')[0]} opacity-20 blur-xl`}
                          animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.2, 0.4, 0.2],
                          }}
                          transition={{
                            duration: 3,
                            repeat: Infinity,
                            delay: index * 0.5,
                          }}
                        />

                        {/* Main Node with Floating Animation */}
                        <motion.div
                          className={`relative w-16 h-16 rounded-full ${nodeGradient} flex items-center justify-center border-4 border-[#111111] z-20 cursor-pointer group`}
                          initial={{ scale: 0, rotate: -180 }}
                          animate={{
                            scale: 1,
                            rotate: 0,
                            y: [0, -8, 0]
                          }}
                          transition={{
                            scale: { delay: index * 0.15, duration: 0.6, type: "spring", stiffness: 200 },
                            rotate: { delay: index * 0.15, duration: 0.6, type: "spring", stiffness: 200 },
                            y: { duration: 4 + index * 0.5, repeat: Infinity, ease: "easeInOut", delay: index * 0.3 }
                          }}
                          whileHover={{
                            scale: 1.15,
                            rotate: 10,
                            y: -12,
                            transition: { duration: 0.2 }
                          }}
                          onClick={() => toggleExpand(update.varizon)}
                        >
                          <UpdateIcon className="h-7 w-7 text-white drop-shadow-lg" />

                          {/* Node Inner Glow */}
                          <div className="absolute inset-2 rounded-full bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        </motion.div>

                        {/* Version Badge */}
                        <motion.div
                          className="absolute -bottom-2 -right-2 bg-[#1a1a1a] border border-gray-600 rounded-full px-2 py-1 text-xs font-bold text-purple-400"
                          initial={{ opacity: 0, scale: 0 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: index * 0.15 + 0.3 }}
                        >
                          v{update.varizon}
                        </motion.div>

                        {/* Connecting Line to Card */}
                        <motion.div
                          className={`absolute top-8 left-16 w-8 h-0.5 bg-gradient-to-r ${connectorStyle} rounded-full`}
                          initial={{ width: 0 }}
                          animate={{ width: 32 }}
                          transition={{ delay: index * 0.15 + 0.5, duration: 0.4 }}
                        />
                      </div>

                      {/* Advanced Content Card */}
                      <motion.div
                        className="flex-1 relative group"
                        initial={{ opacity: 0, x: 50 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.15 + 0.6 }}
                      >
                        {/* Card Background Glow */}
                        <div className={`absolute inset-0 rounded-2xl ${isLatest ? 'bg-gradient-to-r from-yellow-400/10 to-orange-500/10' : 'bg-gradient-to-r from-purple-500/5 to-blue-500/5'} blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500`} />

                        {/* Main Card */}
                        <motion.div
                          className={`relative bg-[#1a1a1a] border rounded-2xl overflow-hidden backdrop-blur-xl transition-all duration-500 ${
                            isLatest
                              ? 'border-yellow-400/30 shadow-[0_0_30px_rgba(251,191,36,0.15)]'
                              : 'border-gray-700/50 hover:border-purple-400/50'
                          } ${isExpanded ? 'shadow-2xl' : 'hover:shadow-xl'}`}
                          whileHover={{
                            y: -8,
                            transition: { duration: 0.3 }
                          }}
                          layout
                        >
                          {/* Latest Version Crown */}
                          {isLatest && (
                            <motion.div
                              className="absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg z-10"
                              initial={{ scale: 0, rotate: -180 }}
                              animate={{ scale: 1, rotate: 0 }}
                              transition={{ delay: index * 0.15 + 0.8, type: "spring" }}
                            >
                              <Crown className="h-4 w-4 text-white" />
                            </motion.div>
                          )}

                          {/* Card Header with Enhanced Design */}
                          <div className="relative p-6 cursor-pointer" onClick={() => toggleExpand(update.varizon)}>
                            {/* Header Background Pattern */}
                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/[0.02] to-transparent" />

                            <div className="relative flex justify-between items-start">
                              <div className="flex-1">
                                <motion.h3
                                  className={`text-2xl font-bold flex items-center gap-3 ${isLatest ? 'text-yellow-400' : 'text-white'}`}
                                  whileHover={{ scale: 1.02 }}
                                >
                                  {update.name || `Version ${update.varizon}`}
                                  {isLatest && (
                                    <motion.span
                                      className="text-xs font-normal bg-gradient-to-r from-yellow-400/20 to-orange-500/20 text-yellow-400 px-3 py-1 rounded-full border border-yellow-400/30"
                                      animate={{
                                        boxShadow: [
                                          '0 0 0 rgba(251, 191, 36, 0)',
                                          '0 0 20px rgba(251, 191, 36, 0.3)',
                                          '0 0 0 rgba(251, 191, 36, 0)'
                                        ]
                                      }}
                                      transition={{ duration: 2, repeat: Infinity }}
                                    >
                                      Latest
                                    </motion.span>
                                  )}
                                </motion.h3>

                                <div className="flex items-center text-sm text-gray-400 mt-3 gap-4">
                                  <div className="flex items-center gap-2">
                                    <Calendar className="h-4 w-4" />
                                    {formatDate(update.release_at)}
                                  </div>
                                  <div className={`px-2 py-1 rounded-full text-xs ${isLatest ? 'bg-yellow-400/10 text-yellow-400' : 'bg-purple-400/10 text-purple-400'}`}>
                                    Release
                                  </div>
                                </div>
                              </div>

                              <motion.button
                                className={`p-3 rounded-full transition-all duration-300 ${
                                  isExpanded
                                    ? (isLatest ? 'bg-yellow-400/20 text-yellow-400' : 'bg-purple-400/20 text-purple-400')
                                    : 'bg-gray-700/50 text-gray-400 hover:text-purple-400 hover:bg-purple-400/10'
                                }`}
                                whileHover={{ scale: 1.1, rotate: 180 }}
                                whileTap={{ scale: 0.95 }}
                              >
                                {isExpanded ? <ArrowUp className="h-5 w-5" /> : <ArrowDown className="h-5 w-5" />}
                              </motion.button>
                            </div>
                          </div>

                          {/* Enhanced Changelog Content */}
                          <AnimatePresence>
                            {isExpanded && (
                              <motion.div
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: "auto" }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.4, ease: "easeInOut" }}
                              >
                                {/* Separator with Gradient */}
                                <div className="relative">
                                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gray-600/20 to-transparent h-px" />
                                  <div className={`absolute inset-0 bg-gradient-to-r from-transparent ${isLatest ? 'via-yellow-400/30' : 'via-purple-400/30'} to-transparent h-px`} />
                                </div>

                                <div className="px-6 py-6">
                                  <div className="text-gray-300 prose prose-invert max-w-none">
                                    {update.changelog ? (
                                      <motion.div
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ delay: 0.1 }}
                                      >
                                        {update.changelog.split('\n').map((line, i) => {
                                          // Check if line is a heading (starts with # or ##)
                                          if (line.startsWith('# ')) {
                                            return (
                                              <motion.h3
                                                key={i}
                                                className="text-xl font-semibold mt-4 mb-3 text-white flex items-center gap-2"
                                                initial={{ opacity: 0, x: -20 }}
                                                animate={{ opacity: 1, x: 0 }}
                                                transition={{ delay: i * 0.05 }}
                                              >
                                                <Sparkles className={`h-5 w-5 ${isLatest ? 'text-yellow-400' : 'text-purple-400'}`} />
                                                {line.replace('# ', '')}
                                              </motion.h3>
                                            );
                                          } else if (line.startsWith('## ')) {
                                            return (
                                              <motion.h4
                                                key={i}
                                                className="text-lg font-semibold mt-3 mb-2 text-gray-200"
                                                initial={{ opacity: 0, x: -15 }}
                                                animate={{ opacity: 1, x: 0 }}
                                                transition={{ delay: i * 0.05 }}
                                              >
                                                {line.replace('## ', '')}
                                              </motion.h4>
                                            );
                                          } else if (line.startsWith('- ')) {
                                            // Enhanced list items
                                            return (
                                              <motion.div
                                                key={i}
                                                className="flex items-start space-x-3 my-2 p-2 rounded-lg hover:bg-gray-800/30 transition-colors"
                                                initial={{ opacity: 0, x: -10 }}
                                                animate={{ opacity: 1, x: 0 }}
                                                transition={{ delay: i * 0.05 }}
                                              >
                                                <div className={`w-2 h-2 rounded-full mt-2 ${isLatest ? 'bg-yellow-400' : 'bg-purple-400'} shadow-lg`} />
                                                <span className="flex-1">{line.replace('- ', '')}</span>
                                              </motion.div>
                                            );
                                          } else if (line === '') {
                                            return <div key={i} className="h-3"></div>; // Empty line spacer
                                          } else {
                                            return (
                                              <motion.p
                                                key={i}
                                                className="my-2 leading-relaxed"
                                                initial={{ opacity: 0 }}
                                                animate={{ opacity: 1 }}
                                                transition={{ delay: i * 0.05 }}
                                              >
                                                {line}
                                              </motion.p>
                                            );
                                          }
                                        })}
                                      </motion.div>
                                    ) : (
                                      <motion.p
                                        className="italic text-gray-400 text-center py-8"
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                      >
                                        No changelog available for this update.
                                      </motion.p>
                                    )}
                                  </div>
                                </div>

                                {/* Enhanced Download Section */}
                                {isLatest && update.link && (
                                  <motion.div
                                    className="mt-6 p-4 bg-gradient-to-r from-yellow-400/5 to-orange-500/5 border-t border-yellow-400/20 rounded-b-lg"
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: 0.3 }}
                                  >
                                    <div className="flex justify-between items-center">
                                      <div className="flex items-center gap-3">
                                        <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                                          <Download className="h-5 w-5 text-white" />
                                        </div>
                                        <div>
                                          <p className="text-white font-semibold">Latest Release</p>
                                          <p className="text-gray-400 text-sm">Ready for download</p>
                                        </div>
                                      </div>

                                      <motion.button
                                        onClick={() => handleDownload(update.link, update.varizon)}
                                        className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-6 py-3 rounded-full font-semibold flex items-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300"
                                        whileHover={{
                                          scale: 1.05,
                                          boxShadow: "0 0 25px rgba(251, 191, 36, 0.4)"
                                        }}
                                        whileTap={{ scale: 0.95 }}
                                      >
                                        <Download className="h-4 w-4" />
                                        Download v{update.varizon}
                                      </motion.button>
                                    </div>
                                  </motion.div>
                                )}
                              </motion.div>
                            )}
                          </AnimatePresence>
                        </motion.div>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </motion.div>
          </div>
        ) : (
          <motion.div
            className="text-center py-16 bg-[#1a1a1a] border border-gray-700/50 rounded-xl backdrop-blur-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h3 className="text-xl font-medium text-white mb-2">No updates available</h3>
            <p className="text-gray-400">Check back later for updates on Pegasus Tool</p>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default WhatsNew;
